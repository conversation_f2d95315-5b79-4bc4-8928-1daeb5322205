import {
  Recipe,
  Ingredient,
  DietPreference,
  MealType,
  InstructionType,
  numRecipesPerMealTypeInitialState,
  RecipeInstructions,
} from '@/app/components/types';
import { LlmService } from '@/app/services/LlmService';
import { UnsplashService } from '@/app/services/UnsplashService';
import { SpoonacularService, SpoonacularSearchParams } from '@/app/services/SpoonacularService';
import { ResponseInputItem } from 'openai/resources/responses/responses';
import { Roles } from '@/constants/Types';
import { recipeGenerationSchema, recipeBasicsSchema, recipeDetailsSchema } from '@/schemas/recipeGeneration';
import MockedRecipeResponse from '@/constants/mocks/MockedRecipeResponse.json';
import { recipeGenerationModel } from '@/constants/LlmConfigs';

async function getUnsplashImage(query: string): Promise<string> {
  const response = await UnsplashService.getImageUrl(query);

  if (response.success && response.imageUrl) {
    return response.imageUrl;
  }

  // Log the error for debugging but return fallback image
  console.error(`Failed to fetch image for query "${query}":`, response.error);
  return 'https://via.placeholder.com/300x200?text=No+Image';
}

/**
 * Generate recipes from both LLM and Spoonacular APIs in parallel
 * Returns whichever responds first
 *
 * @returns An array of recipes with basic info and the response ID for future API calls
 */
export async function generateRecipeBasicsAsync(
  ingredients: Ingredient[],
  dietPreferences: DietPreference | null,
  numRecipesPerMealType: Partial<{ [key in MealType]: number }> = numRecipesPerMealTypeInitialState,
  existingRecipeTitles: string[] = []
): Promise<{ recipes: Recipe[]; responseId: string }> {
  // You can set this to true to use mock data without environment variables
  const useMockData = process.env.USE_MOCK_RECIPES === 'true';
  console.log('Using mock data for recipe basics:', useMockData);

  if (useMockData) {
    // For mock data, we'll use the same data but strip out ingredients and instructions
    const mockedRecipes = await Promise.all(
      MockedRecipeResponse.recipes.map(async (recipe: any) => {
        const imageUrl = await getUnsplashImage(recipe.imageQuery || recipe.title);
        return {
          id: recipe.id || Math.random().toString(36).substring(2, 9),
          title: recipe.title,
          timeInMinutes: recipe.timeInMinutes,
          calories: recipe.calories,
          imageUrl,
          mealType: recipe.mealType,
          source: 'llm' as const,
          // Empty placeholders for ingredients and instructions
          ingredients: [],
          instructions: {
            [InstructionType.HIGH_LEVEL]: '',
            [InstructionType.DETAILED]: '',
            [InstructionType.TEACH_MODE]: '',
          },
        };
      })
    );
    // For mock data, return a fake response ID
    return { recipes: mockedRecipes, responseId: 'mock-response-id' };
  }

  // Create promises for both LLM and Spoonacular API calls
  const llmPromise = generateRecipeBasicsFromLLM(
    ingredients,
    dietPreferences,
    numRecipesPerMealType,
    existingRecipeTitles
  );
  const spoonacularPromise = generateRecipeBasicsFromSpoonacular(ingredients, dietPreferences, numRecipesPerMealType);

  try {
    // Use Promise.race to return whichever API responds first
    const result = await Promise.race([llmPromise, spoonacularPromise]);
    console.log(`Recipe generation completed using ${result.recipes[0]?.source || 'unknown'} source`);
    return result;
  } catch (error) {
    console.error('Both recipe generation methods failed:', error);
    // Fallback to error recipes
    return {
      recipes: [
        {
          id: 'error-recipe',
          title: 'Error Loading Recipes',
          timeInMinutes: 0,
          calories: 0,
          imageUrl: 'https://via.placeholder.com/300x200?text=Error',
          ingredients: [],
          instructions: {
            [InstructionType.HIGH_LEVEL]: '',
            [InstructionType.DETAILED]: '',
            [InstructionType.TEACH_MODE]: '',
          },
          mealType: MealType.BREAKFAST,
          source: 'llm',
        },
      ],
      responseId: 'error-response-id',
    };
  }
}

/**
 * Generate basic recipe information using LLM
 */
async function generateRecipeBasicsFromLLM(
  ingredients: Ingredient[],
  dietPreferences: DietPreference | null,
  numRecipesPerMealType: Partial<{ [key in MealType]: number }>,
  existingRecipeTitles: string[]
): Promise<{ recipes: Recipe[]; responseId: string }> {
  const instructions = `You are a professional chef and nutritionist. Generate detailed, practical recipes. Always return a valid JSON object with a "recipes" array.
If user has very limited ingredients, you can use ingredients they don't have. The most important thing is to make recipes that are tailored to their need and realistic. Never sacrifice user preference and nutrition for the sake of using the provided ingredients.
Those are user's dietary preferences: ${JSON.stringify(dietPreferences)}. The recipes you generate should follow this guideline. Don't repeat any existing recipes.
For each recipe, provide:
- A unique ID
- A title
- Preparation time in minutes (do your best to estimate how long it will take to prepare the whole dish from start to finish)
- Calorie count (per serving)
- A search query for a relevant dish image. Use two words. Make sure people can tell it's a food item from the two words.
- A meal type ${Object.values(MealType).join(', ')}`;

  try {
    let prompt = `Generate x number of recipes for each meal type using these ingredients: ${ingredients.map((ing) => ing.name).join(', ')}.
        Get x and what meal types you should generate from this JSON: ${JSON.stringify(numRecipesPerMealType)},
        Return your response as a JSON object with a "recipes" array.
        `;
    if (existingRecipeTitles.length > 0) {
      prompt += `Don't generate recipes that are already in this list: ${existingRecipeTitles.join(', ')}.`;
    }

    const input: ResponseInputItem[] = [
      {
        role: Roles.developer,
        content: prompt,
      },
    ];

    const response = await LlmService.callLlmApi(
      recipeGenerationModel,
      instructions,
      input,
      1,
      recipeBasicsSchema,
      'recipes'
    );

    const outputText = LlmService.extractOutputText(response);

    if (!outputText) {
      console.error('No response content from LLM API');
      throw new Error('No response from LLM API');
    }

    const parsedResponse = JSON.parse(outputText);

    if (!parsedResponse.recipes || !Array.isArray(parsedResponse.recipes)) {
      console.error('Invalid response format:', parsedResponse);
      throw new Error('Invalid response format from LLM');
    }

    console.log('Recipe basics generated from LLM:', parsedResponse.recipes);

    // Generate images for each recipe in parallel
    const recipesWithImages = await Promise.all(
      parsedResponse.recipes.map(async (recipe: any) => {
        const imageUrl = await getUnsplashImage(recipe.imageQuery || recipe.title);
        return {
          ...recipe,
          id: recipe.id || Math.random().toString(36).substring(2, 9),
          imageUrl,
          source: 'llm' as const,
          // Empty placeholders for ingredients and instructions
          ingredients: [],
          instructions: {
            [InstructionType.HIGH_LEVEL]: '',
            [InstructionType.DETAILED]: '',
            [InstructionType.TEACH_MODE]: '',
          },
        };
      })
    );

    return {
      recipes: recipesWithImages,
      responseId: response.id,
    };
  } catch (error) {
    console.error('Error generating recipe basics from LLM:', error);
    throw error;
  }
}

/**
 * Generate basic recipe information using Spoonacular API
 */
async function generateRecipeBasicsFromSpoonacular(
  ingredients: Ingredient[],
  dietPreferences: DietPreference | null,
  numRecipesPerMealType: Partial<{ [key in MealType]: number }>
): Promise<{ recipes: Recipe[]; responseId: string }> {
  try {
    // Generate search queries from ingredients
    const ingredientNames = ingredients.map((ing) => ing.name);
    const searchQueries = SpoonacularService.generateSearchQueries(ingredientNames, dietPreferences);

    // Use the first search query for now
    const query = searchQueries[0] || 'healthy recipe';

    // Calculate total number of recipes needed
    const totalRecipes = Object.values(numRecipesPerMealType).reduce((sum, count) => sum + (count || 0), 0);

    const searchParams: SpoonacularSearchParams = {
      query,
      diet: dietPreferences?.diet,
      intolerances: dietPreferences?.allergy,
      number: Math.min(totalRecipes, 10), // Limit to 10 recipes max
    };

    const response = await SpoonacularService.getRecipes(searchParams);

    if (!response.success || !response.recipes) {
      throw new Error(response.error || 'Failed to fetch recipes from Spoonacular');
    }

    console.log('Recipe basics generated from Spoonacular:', response.recipes);

    return {
      recipes: response.recipes,
      responseId: 'spoonacular-response-id',
    };
  } catch (error) {
    console.error('Error generating recipe basics from Spoonacular:', error);
    throw error;
  }
}

/**
 * Generate detailed recipe information (ingredients and instructions) for a specific recipe
 * This is used when a user clicks on a recipe to view details
 *
 * Uses the previous response ID to maintain context, so the model already has access to
 * the user's ingredients and dietary preferences from the previous conversation.
 *
 * @param recipeId The ID of the recipe to get details for
 * @param recipeTitle The title of the recipe
 * @param mealType The meal type of the recipe
 * @param previousResponseId The ID of the previous response that generated the basic recipe info
 * @returns The ingredients and instructions for the recipe
 */
export async function generateRecipeDetailsAsync(
  recipeId: string,
  recipeTitle: string,
  mealType: MealType,
  previousResponseId: string
): Promise<{ ingredients: Ingredient[]; instructions: RecipeInstructions }> {
  // You can set this to true to use mock data without environment variables
  const useMockData = process.env.USE_MOCK_RECIPES === 'true';
  console.log('Using mock data for recipe details:', useMockData);

  if (useMockData) {
    // Find the recipe in the mock data
    const mockRecipe = MockedRecipeResponse.recipes.find(
      (recipe: any) => recipe.id === recipeId || recipe.title === recipeTitle
    );

    if (mockRecipe) {
      return {
        ingredients: mockRecipe.ingredients,
        instructions: {
          [InstructionType.HIGH_LEVEL]:
            mockRecipe.instructions['High level'] || 'No high-level instructions available.',
          [InstructionType.DETAILED]: mockRecipe.instructions['Detailed'] || 'No detailed instructions available.',
          [InstructionType.TEACH_MODE]:
            mockRecipe.instructions['Teach mode'] || 'No teach mode instructions available.',
        },
      };
    }

    // If not found, return default mock data
    return {
      ingredients: [
        { name: 'Mock ingredient 1', available: true },
        { name: 'Mock ingredient 2', available: false },
      ],
      instructions: {
        [InstructionType.HIGH_LEVEL]: 'Mock high-level instructions',
        [InstructionType.DETAILED]: 'Mock detailed instructions',
        [InstructionType.TEACH_MODE]: 'Mock teach mode instructions',
      },
    };
  }

  const instructions = `You are a professional chef and nutritionist. Generate all ingredients needed and instructions for the recipe "${recipeTitle}" (${mealType}).
You already have information about the user's available ingredients and dietary preferences from our previous conversation.

Provide:
1. A list of ingredients with availability (true if it's in the user's ingredients list, false otherwise)
2. Three versions of step-by-step instructions:
   - High Level: Brief, concise instructions for experienced cooks
   - Detailed: Comprehensive instructions with more explanation. Each step should be on a separate line.
   - Teach Mode: Extremely detailed instructions with explanations of techniques, tips, and the science behind cooking steps. Each step should be on a separate line.

Return your response as a JSON object with "ingredients" and "instructions" properties.`;

  try {
    const input: ResponseInputItem[] = [
      {
        role: Roles.developer,
        content: `Generate detailed ingredients and instructions for the recipe "${recipeTitle}" (${mealType}).`,
      },
    ];

    // Use the previous response ID to maintain context
    const response = await LlmService.callLlmApi(
      recipeGenerationModel,
      instructions,
      input,
      1,
      recipeDetailsSchema,
      'recipeDetails',
      previousResponseId
    );

    const outputText = LlmService.extractOutputText(response);

    if (!outputText) {
      console.error('No response content from LLM API');
      throw new Error('No response from LLM API');
    }

    const parsedResponse = JSON.parse(outputText);

    if (!parsedResponse.ingredients || !parsedResponse.instructions) {
      console.error('Invalid response format:', parsedResponse);
      return {
        ingredients: [{ name: 'Error loading ingredients', available: false }],
        instructions: {
          [InstructionType.HIGH_LEVEL]: 'There was an error generating instructions. Please try again later.',
          [InstructionType.DETAILED]: 'There was an error generating instructions. Please try again later.',
          [InstructionType.TEACH_MODE]: 'There was an error generating instructions. Please try again later.',
        },
      };
    }

    console.log('Recipe details generated for:', recipeTitle);

    return {
      ingredients: parsedResponse.ingredients,
      instructions: {
        [InstructionType.HIGH_LEVEL]:
          parsedResponse.instructions['High level'] || 'No high-level instructions available.',
        [InstructionType.DETAILED]: parsedResponse.instructions['Detailed'] || 'No detailed instructions available.',
        [InstructionType.TEACH_MODE]:
          parsedResponse.instructions['Teach mode'] || 'No teach mode instructions available.',
      },
    };
  } catch (error) {
    console.error('Error generating recipe details:', error);
    return {
      ingredients: [{ name: 'Error loading ingredients', available: false }],
      instructions: {
        [InstructionType.HIGH_LEVEL]: 'There was an error generating instructions. Please try again later.',
        [InstructionType.DETAILED]: 'There was an error generating instructions. Please try again later.',
        [InstructionType.TEACH_MODE]: 'There was an error generating instructions. Please try again later.',
      },
    };
  }
}

/**
 * Legacy function that generates complete recipes in one call
 * Kept for backward compatibility
 */
export async function generateRecipesAsync(
  ingredients: Ingredient[],
  dietPreferences: DietPreference | null,
  numRecipesPerMealType: Partial<{ [key in MealType]: number }> = numRecipesPerMealTypeInitialState,
  existingRecipeTitles: string[] = []
): Promise<Recipe[]> {
  // You can set this to true to use mock data without environment variables
  const useMockData = process.env.USE_MOCK_RECIPES === 'true';
  console.log('Using mock data:', useMockData);

  if (useMockData) {
    const mockedRecipes = await Promise.all(
      MockedRecipeResponse.recipes.map(async (recipe: any) => {
        const imageUrl = await getUnsplashImage(recipe.imageQuery || recipe.title);
        return {
          ...recipe,
          id: recipe.id || Math.random().toString(36).substring(2, 9),
          imageUrl,
          source: 'llm' as const,
          instructions: {
            [InstructionType.HIGH_LEVEL]: recipe.instructions['High level'] || 'No high-level instructions available.',
            [InstructionType.DETAILED]: recipe.instructions['Detailed'] || 'No detailed instructions available.',
            [InstructionType.TEACH_MODE]: recipe.instructions['Teach mode'] || 'No teach mode instructions available.',
          },
        };
      })
    );
    return mockedRecipes;
  }

  const instructions = `You are a professional chef and nutritionist. Generate detailed, practical recipes. For each recipe, provide three versions of instructions: high-level, detailed, and teach mode. Always return a valid JSON object with a "recipes" array.
You don't need to use all ingredients or limited to just these ingredients. Hit a balance between using the provided ingredients and creating tasty dishes.
Those are user's dietary preferences: ${JSON.stringify(dietPreferences)}. The recipes you generate should follow this guideline. Don't repeat any existing recipes, if there are limited ingredients, feel free to use other ingredients not given to you.
For each recipe, provide:
- A unique ID
- A title
- Preparation time in minutes (do your best to estimate how long it will take to prepare the whole dish from start to finish)
- Calorie count (per serving)
- List of ingredients with availability
- Three versions of step-by-step instructions:
  1. High Level: Brief, concise instructions for experienced cooks
  2. Detailed: Comprehensive instructions with more explanation
  3. Teach Mode: Extremely detailed instructions with explanations of techniques, tips, and the science behind cooking steps. Each step should be on a separate line.
- A search query for a relevant dish image. Use two words.
- A meal type ${Object.values(MealType).join(', ')}`;

  try {
    let prompt = `Generate x number of recipes for each meal type using these ingredients: ${ingredients.map((ing) => ing.name).join(', ')}.
        Get x and what meal types you should generate from this JSON: ${JSON.stringify(numRecipesPerMealType)},
        Return your response as a JSON object with a "recipes" array.
        `;
    if (existingRecipeTitles.length > 0) {
      prompt += `Don't generate recipes that are already in this list: ${existingRecipeTitles.join(', ')}.`;
    }

    const input: ResponseInputItem[] = [
      {
        role: Roles.developer,
        content: prompt,
      },
    ];

    const response = await LlmService.callLlmApi(
      recipeGenerationModel,
      instructions,
      input,
      1,
      recipeGenerationSchema,
      'recipes'
    );

    const outputText = LlmService.extractOutputText(response);

    if (!outputText) {
      console.error('No response content from LLM API');
      throw new Error('No response from LLM API');
    }

    const parsedResponse = JSON.parse(outputText);

    if (!parsedResponse.recipes || !Array.isArray(parsedResponse.recipes)) {
      console.error('Invalid response format:', parsedResponse);
      return Object.values(MealType).map((mealType) => ({
        id: 'mock-recipe',
        title: 'Sample Recipe',
        timeInMinutes: 30,
        calories: 600,
        imageUrl: 'https://via.placeholder.com/300x200?text=Sample',
        ingredients: [],
        source: 'llm' as const,
        instructions: {
          [InstructionType.HIGH_LEVEL]: 'There was an error generating recipes. Please try again later.',
          [InstructionType.DETAILED]: 'There was an error generating recipes. Please try again later.',
          [InstructionType.TEACH_MODE]: 'There was an error generating recipes. Please try again later.',
        },
        mealType: mealType,
      }));
    }

    console.log('Recipes generated:', parsedResponse.recipes);

    // Generate images for each recipe in parallel
    const recipesWithImages = await Promise.all(
      parsedResponse.recipes.map(async (recipe: any) => {
        const imageUrl = await getUnsplashImage(recipe.imageQuery || recipe.title);
        return {
          ...recipe,
          id: recipe.id || Math.random().toString(36).substring(2, 9),
          imageUrl,
          source: 'llm' as const,
          // Ensure instructions are in the correct format
          instructions: {
            [InstructionType.HIGH_LEVEL]: recipe.instructions['High level'] || 'No high-level instructions available.',
            [InstructionType.DETAILED]: recipe.instructions['Detailed'] || 'No detailed instructions available.',
            [InstructionType.TEACH_MODE]: recipe.instructions['Teach mode'] || 'No teach mode instructions available.',
          },
        };
      })
    );

    return recipesWithImages;
  } catch (error) {
    console.error('Error generating recipes:', error);
    return [
      {
        id: 'error-recipe',
        title: 'Error Loading Recipes',
        timeInMinutes: 0,
        calories: 0,
        imageUrl: 'https://via.placeholder.com/300x200?text=Error',
        ingredients: [],
        source: 'llm' as const,
        instructions: {
          [InstructionType.HIGH_LEVEL]: 'There was an error generating recipes. Please try again later.',
          [InstructionType.DETAILED]: 'There was an error generating recipes. Please try again later.',
          [InstructionType.TEACH_MODE]: 'There was an error generating recipes. Please try again later.',
        },
        mealType: MealType.BREAKFAST,
      },
    ];
  }
}